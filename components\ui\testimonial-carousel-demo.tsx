"use client";

import { useState, useEffect } from "react";
import { TestimonialCarousel } from "@/components/ui/testimonial-carousel";
import { useFeaturedTestimonials } from "@/hooks/useTestimonials";

export function TestimonialCarouselDemo() {
  const { testimonials, loading, error } = useFeaturedTestimonials();
  const [testimonialData, setTestimonialData] = useState([
    // Default YouTube testimonials as fallback
    {
      title: "Exceptional Quality",
      quote: "Laxmi Developers delivered beyond our expectations. The attention to detail and quality of construction is outstanding. We're extremely happy with our investment.",
      name: "<PERSON><PERSON>",
      role: "Homeowner, Millennium Park",
      youtubeUrl: "https://youtu.be/MUDMUMetgnw",
      thumbnailSrc: "/images/projects/Millennium Park.jpg",
    },
    {
      title: "Professional Service",
      quote: "The team at Laxmi Developers was professional from start to finish. They guided us through the entire process and delivered exactly what was promised.",
      name: "<PERSON><PERSON> <PERSON>",
      role: "Business Owner, Business Hub",
      youtubeUrl: "https://youtu.be/IrOBu0q6fQI",
      thumbnailSrc: "/images/projects/Millennium Business Hub.jpg",
    },
    {
      title: "Timely Delivery",
      quote: "What impressed me most was how Laxmi Developers completed the project on schedule. The construction quality is excellent and the design is modern and functional.",
      name: "Amit Desai",
      role: "Investor, Laxmi Nova",
      youtubeUrl: "https://youtu.be/9Bdx26mKPyc?si=_dwZIcW-g4Heqj2n",
      thumbnailSrc: "/images/projects/Laxmi Nova.jpg",
    },
    {
      title: "Great Investment",
      quote: "Investing with Laxmi Developers was one of the best decisions I made. The returns have been excellent and the property value has appreciated significantly.",
      name: "Meera Joshi",
      role: "Property Investor, Laxmi Heights",
      youtubeUrl: "https://youtu.be/3bmdwCqPiBA?si=0us38hGICW0rQg7y",
      thumbnailSrc: "/images/projects/Laxmi Heights.jpg",
    },
  ]);

  // Update testimonial data when real data is loaded
  useEffect(() => {
    if (testimonials && testimonials.length > 0) {
      const transformedTestimonials = testimonials.map(testimonial => ({
        title: testimonial.designation || "Client Testimonial",
        quote: testimonial.content,
        name: testimonial.name,
        role: `${testimonial.designation || "Client"}${testimonial.projectId?.title ? `, ${testimonial.projectId.title}` : ''}`,
        youtubeUrl: testimonial.youtubeUrl,
        videoSrc: undefined, // We're using YouTube URLs now
        thumbnailSrc: testimonial.image || "/images/default-avatar.jpg",
      }));
      setTestimonialData(transformedTestimonials);
    }
  }, [testimonials]);

  return (
    <div className="relative overflow-hidden w-full py-20 bg-gradient-to-b from-background to-background/90">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Clients Say</h2>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
            Hear directly from our satisfied clients about their experience working with Laxmi Developers.
            Watch their video testimonials below.
          </p>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="text-foreground/60 mt-4">Loading testimonials...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">Failed to load testimonials</p>
            <TestimonialCarousel
              testimonials={testimonialData}
              autoScroll={true}
              scrollInterval={4000}
            />
          </div>
        ) : (
          <TestimonialCarousel
            testimonials={testimonialData}
            autoScroll={true}
            scrollInterval={4000}
          />
        )}
      </div>
    </div>
  );
}